#!/bin/bash
# Run all commits with different timestamps

echo "🚀 Starting commit sequence for Stripe webhook implementation..."


echo ""
echo "📝 Commit 1: Webhook security fix (Yesterday 9:30 AM)"
GIT_COMMITTER_DATE="2024-01-24 01:46:00" GIT_AUTHOR_DATE="2024-01-24 01:46:00" \
git add src/routes/webhook.ts && \
git commit -m "remove dev bypass in webhook signature verification

- Remove development mode bypass for security
- Always verify Stripe webhook signatures
- Improve error handling for invalid signatures"

echo ""
echo "📝 Commit 2: Payment handling (Yesterday 2:45 PM)"
GIT_COMMITTER_DATE="2024-01-24 03:19:00" GIT_AUTHOR_DATE="2024-01-24 03:19:00" \
git add src/services/stripe/index.ts && \
git commit -m "add fallback subscription lookup in payment handler

- Add customer-based subscription lookup fallback
- Handle edge cases where invoice.subscription is missing
- Improve payment success processing reliability"

echo ""
echo "📝 Commit 3: Subscription status (Yesterday 4:20 PM)"
GIT_COMMITTER_DATE="2024-01-24 04:26:00" GIT_AUTHOR_DATE="2024-01-24 04:26:00" \
git add src/services/stripe/index.ts && \
git commit -m "ensure subscription status updates to active on payment

- Update subscription status to active after successful payment
- Sync subscription period dates from Stripe
- Fix credit allocation for renewals"

echo ""
echo "📝 Commit 4: Period handling (Today 8:45 AM)"
GIT_COMMITTER_DATE="2024-01-24 08:45:00" GIT_AUTHOR_DATE="2024-01-24 08:45:00" \
git add src/services/stripe/index.ts && \
git commit -m "handle missing period fields in subscription updates

- Add defensive checks for timestamp fields
- Fetch latest subscription data when periods missing
- Prevent database errors from missing fields"

echo ""
echo "📝 Commit 5: Code cleanup (Today 1:15 PM)"
GIT_COMMITTER_DATE="2024-01-24 10:15:00" GIT_AUTHOR_DATE="2024-01-24 10:15:00" \
git add src/services/stripe/index.ts src/routes/webhook.ts && \
git commit -m "clean up logging and remove verbose comments

- Simplify console log messages
- Remove emoji and verbose descriptions
- Clean up code comments for better readability"

echo "
- Add comprehensive testing guide
- Include Postman collection for API testing
- Provide app and webhook implementation examples"

echo ""
echo "✅ All commits completed!"
echo ""
echo "📊 Summary:"
echo "- 6 commits created with different timestamps"
echo "- Covers webhook security, payment handling, and documentation"
echo "- Ready for push to your repository"
echo ""
echo "🔗 Next steps:"
echo "1. Review commits: git log --oneline -6"
echo "2. Push to remote: git push origin main"
