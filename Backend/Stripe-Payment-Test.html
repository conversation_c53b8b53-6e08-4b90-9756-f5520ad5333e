<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Stripe Payment Test</title>
  <script src="https://js.stripe.com/v3/"></script>
  <style>
    body { font-family: sans-serif; padding: 30px; }
    #card-element { padding: 10px; border: 1px solid #ccc; border-radius: 6px; margin-bottom: 15px; }
    #pay-button { padding: 10px 20px; background: #6772e5; color: white; border: none; border-radius: 6px; cursor: pointer; }
  </style>
</head>
<body>
  <h2>Stripe Payment Test</h2>
  <div id="card-element"></div>
  <button id="pay-button">Pay</button>
  <div id="result"></div>

  <script>
    const stripe = Stripe("pk_test_51S2voWFocBKD2A5ZWntDyp14purYB2nDj2mRHWH0SYAzWUyTX4DhJbRXHfxKx1cLqBtwKVVuOzw0CDVROqbHRVi600EDvHnFQV");

    const clientSecret = "pi_3SAkOLFocBKD2A5Z006Vma6W_secret_uu7nCku2hDrOeyzt8yKogwVhn";

    const elements = stripe.elements();
    const cardElement = elements.create("card");
    cardElement.mount("#card-element");

    document.getElementById("pay-button").addEventListener("click", async () => {
      const { paymentIntent, error } = await stripe.confirmCardPayment(clientSecret, {
        payment_method: {
          card: cardElement,
        }
      });

      if (error) {
        document.getElementById("result").innerText = "❌ Payment failed: " + error.message;
      } else {
        document.getElementById("result").innerText = "✅ Payment succeeded! ID: " + paymentIntent.id;
      }
    });
  </script>
</body>
</html>