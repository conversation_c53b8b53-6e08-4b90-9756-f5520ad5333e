#!/bin/bash
# Simple commit script for current work

echo "🚀 Creating commit for current work..."

# Add and commit the current files
echo "📝 Adding commit planning files..."
git add commit-plan.md commits/

# Create a simple commit
git commit -m "docs: add commit planning and organization

- Add commit planning structure for Stripe webhook work
- Include commit scripts for organized development
- Prepare for systematic code commits"

echo "✅ Commit created successfully!"
echo ""
echo "📊 Summary:"
echo "- Added commit planning and organization files"
echo "- Ready for additional development work"
echo ""
echo "🔗 Next steps:"
echo "1. Review commit: git log --oneline -1"
echo "2. Continue with development work"
