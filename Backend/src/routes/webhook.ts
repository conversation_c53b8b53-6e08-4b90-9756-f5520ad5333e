import express from 'express'
import { StripeService } from '@/services/stripe'
import WebhookSecurity from '@/utils/webhook-security'

const router = express.Router()

/**
 * Stripe webhook endpoint with proper signature verification
 * This endpoint uses express.raw middleware to get the raw body
 * for webhook signature verification
 */
router.post(
  '/stripe',
  express.raw({ type: 'application/json' }),
  ...WebhookSecurity.stripeWebhookSecurity(),
  async (req, res) => {
    const startTime = Date.now()

    try {
      const signature = req.get('stripe-signature')

      if (!signature) {
        console.error('❌ Missing Stripe signature header')
        return res.status(400).json({
          success: false,
          error: 'Missing Stripe signature',
        })
      }

      // Verify webhook signature and construct event
      let event
      try {
        // req.body is a Buffer from express.raw middleware
        // Pass it directly to Strip<PERSON> for signature verification
        event = StripeService.verifyWebhookSignature(req.body, signature)
        console.log(`✅ Webhook signature verified for event: ${event.type} (${event.id})`)
      } catch (error) {
        console.error('❌ Webhook signature verification failed:', error)

        // In production, always reject invalid signatures
        // Remove development bypass for security
        return res.status(400).json({
          success: false,
          error: 'Invalid webhook signature',
        })
      }

      console.log(`📥 Processing Stripe webhook: ${event.type} (${event.id})`)

      // Process the webhook event with proper error handling
      await StripeService.processWebhookEvent(event)

      const processingTime = Date.now() - startTime
      console.log(`✅ Webhook processed successfully in ${processingTime}ms: ${event.type} (${event.id})`)

      // Return 200 status to Stripe to prevent retries
      res.status(200).json({
        success: true,
        received: true,
        eventId: event.id,
        eventType: event.type,
        processingTime,
      })
    } catch (error) {
      const processingTime = Date.now() - startTime
      console.error(`❌ Webhook processing error (${processingTime}ms):`, error)

      // Return 400 status to Stripe for client errors
      // Stripe will retry 500 errors but not 400 errors
      const statusCode = error instanceof Error && error.message.includes('already processed') ? 200 : 400

      res.status(statusCode).json({
        success: false,
        error: error instanceof Error ? error.message : 'Webhook processing failed',
        processingTime,
      })
    }
  }
)

export default router
